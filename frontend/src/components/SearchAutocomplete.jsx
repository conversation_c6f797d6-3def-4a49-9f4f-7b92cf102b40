import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaSearch, FaTimes, FaGamepad, FaTag } from 'react-icons/fa';
import PropTypes from 'prop-types';
import { useLanguage } from '../context/LanguageContext';
import { getAllGames } from '../services/gameService';
import { getGameCategories } from '../utils/categoryUtils';
import { startsWithTerm } from '../utils/searchUtils';

/**
 * SearchAutocomplete Component
 * Provides a search input with autocomplete suggestions for games
 */
const SearchAutocomplete = ({ className = '', placeholder, onSearch }) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [isOpen, setIsOpen] = useState(false);

  const [allGames, setAllGames] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const navigate = useNavigate();
  const { t } = useLanguage();
  const inputRef = useRef(null);
  const dropdownRef = useRef(null);

  // Fetch all games on component mount for autocomplete
  useEffect(() => {
    const fetchGames = async () => {
      try {
        const response = await getAllGames({ limit: 100 }); // Get more games for better autocomplete
        const games = response.games || [];
        setAllGames(games);
      } catch (error) {
        console.error('Error fetching games for autocomplete:', error);
      }
    };

    fetchGames();
  }, []);

  // Generate suggestions based on query
  useEffect(() => {
    if (!query.trim() || allGames.length === 0) {
      setSuggestions([]);
      setSelectedIndex(-1);
      return;
    }

    const searchTerm = query.toLowerCase();

    const filtered = allGames
      .filter(game => {
        // Only include games where title, genre, tags, or description STARTS with the search term
        // No fallback partial matches to ensure strict word-boundary matching
        const titleStartsMatch = startsWithTerm(game.title, searchTerm);
        const genreStartsMatch = startsWithTerm(game.genre, searchTerm);
        const tagsStartsMatch = game.tags && startsWithTerm(game.tags, searchTerm);
        const descStartsMatch = startsWithTerm(game.description, searchTerm);

        return titleStartsMatch || genreStartsMatch || tagsStartsMatch || descStartsMatch;
      })
      // Sort results by relevance (title matches first, then genre, then tags, then description)
      .sort((a, b) => {
        const aStartsTitle = startsWithTerm(a.title, searchTerm);
        const bStartsTitle = startsWithTerm(b.title, searchTerm);
        const aStartsGenre = startsWithTerm(a.genre, searchTerm);
        const bStartsGenre = startsWithTerm(b.genre, searchTerm);
        const aStartsTags = a.tags && startsWithTerm(a.tags, searchTerm);
        const bStartsTags = b.tags && startsWithTerm(b.tags, searchTerm);

        // Priority order: Title > Genre > Tags > Description
        if (aStartsTitle && !bStartsTitle) return -1;
        if (!aStartsTitle && bStartsTitle) return 1;
        if (aStartsGenre && !bStartsGenre) return -1;
        if (!aStartsGenre && bStartsGenre) return 1;
        if (aStartsTags && !bStartsTags) return -1;
        if (!aStartsTags && bStartsTags) return 1;

        // If both have same priority, sort alphabetically
        return a.title.localeCompare(b.title);
      })
      .slice(0, 6) // Limit to 6 games to leave room for categories
      .map(game => ({
        id: game.id,
        title: game.title,
        genre: game.genre,
        image: game.cardImage || game.image,
        type: 'game'
      }));

    // Add category suggestions from the centralized categories with translation support
    const categories = getGameCategories()
      .filter(category => {
        // Get translated category name using the translation function
        const translatedName = t(`sidebar.navigation.${category.value}`);
        const categoryLabel = category.label.toLowerCase();
        const categoryValue = category.value.toLowerCase();
        const translatedNameLower = translatedName.toLowerCase();

        // Check if search term matches the start of any category identifier
        return startsWithTerm(category.label, searchTerm) ||
               startsWithTerm(category.value, searchTerm) ||
               startsWithTerm(translatedName, searchTerm) ||
               // Only include partial matches for very short search terms (1-2 characters)
               (searchTerm.length <= 2 && (
                 categoryLabel.includes(searchTerm) ||
                 categoryValue.includes(searchTerm) ||
                 translatedNameLower.includes(searchTerm)
               ));
      })
      .slice(0, 3)
      .map(category => ({
        id: `category-${category.value}`,
        title: t(`sidebar.navigation.${category.value}`), // Use translated name
        slug: category.slug,
        type: 'category'
      }));

    // Add genre suggestions from actual game data (for genres not in categories)
    const gameGenres = [...new Set(allGames.map(game => game.genre))]
      .filter(genre => {
        if (!genre) return false;
        // Don't include if it's already in categories
        const isInCategories = getGameCategories().some(cat =>
          cat.label.toLowerCase() === genre.toLowerCase() ||
          cat.value.toLowerCase() === genre.toLowerCase()
        );
        if (isInCategories) return false;

        return startsWithTerm(genre, searchTerm) ||
               (searchTerm.length <= 2 && genre.toLowerCase().includes(searchTerm));
      })
      .slice(0, 2)
      .map(genre => ({
        id: `genre-${genre}`,
        title: genre,
        type: 'genre'
      }));

    const allSuggestions = [...filtered, ...categories, ...gameGenres];
    setSuggestions(allSuggestions);
    setSelectedIndex(-1);
  }, [query, allGames, t]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target) &&
        !inputRef.current?.contains(event.target)
      ) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle input change
  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    setIsOpen(value.trim().length > 0);
  };

  // Handle search submission
  const handleSearch = (searchQuery = query) => {
    if (!searchQuery.trim()) return;
    
    setIsOpen(false);
    setSelectedIndex(-1);
    
    if (onSearch) {
      onSearch(searchQuery);
    } else {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    if (suggestion.type === 'game') {
      navigate(`/game/${suggestion.id}`);
    } else if (suggestion.type === 'category') {
      navigate(`/category/${suggestion.slug}`);
    } else if (suggestion.type === 'genre') {
      navigate(`/search?genre=${encodeURIComponent(suggestion.title)}`);
    }
    setQuery('');
    setIsOpen(false);
    setSelectedIndex(-1);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!isOpen || suggestions.length === 0) {
      if (e.key === 'Enter') {
        handleSearch();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionClick(suggestions[selectedIndex]);
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Clear search
  const handleClear = () => {
    setQuery('');
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm z-10" />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => query.trim() && setIsOpen(true)}
          placeholder={placeholder || t('search.placeholder')}
          className="w-full pl-12 pr-12 py-3 bg-[#2a2a2a] border border-gray-600 rounded-full text-white placeholder-gray-400 focus:outline-none focus:border-[#f44336] focus:ring-2 focus:ring-[#f44336]/20 transition-all duration-200"
        />
        {query && (
          <button
            onClick={handleClear}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors duration-200"
            aria-label="Clear search"
          >
            <FaTimes className="text-sm" />
          </button>
        )}
      </div>

      {/* Autocomplete Dropdown */}
      {isOpen && suggestions.length > 0 && (
        <div
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 mt-2 bg-[#2a2a2a] border border-gray-600 rounded-lg shadow-xl z-50 max-h-80 overflow-y-auto"
        >
          {suggestions.map((suggestion, index) => (
            <button
              key={suggestion.id}
              onClick={() => handleSuggestionClick(suggestion)}
              className={`w-full flex items-center space-x-3 px-4 py-3 text-left transition-colors duration-200 ${
                index === selectedIndex
                  ? 'bg-[#f44336] text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              } ${index === 0 ? 'rounded-t-lg' : ''} ${
                index === suggestions.length - 1 ? 'rounded-b-lg' : ''
              }`}
            >
              {suggestion.type === 'game' ? (
                <>
                  {suggestion.image ? (
                    <img
                      src={suggestion.image}
                      alt={suggestion.title}
                      className="w-8 h-8 rounded object-cover flex-shrink-0"
                    />
                  ) : (
                    <div className="w-8 h-8 bg-gray-600 rounded flex items-center justify-center flex-shrink-0">
                      <FaGamepad className="text-gray-400 text-sm" />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{suggestion.title}</div>
                    <div className="text-xs text-gray-400 truncate">{suggestion.genre}</div>
                  </div>
                </>
              ) : suggestion.type === 'category' ? (
                <>
                  <div className="w-8 h-8 bg-gradient-to-r from-[#7b52ff] to-[#9b7dff] rounded flex items-center justify-center flex-shrink-0">
                    <FaTag className="text-white text-sm" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{suggestion.title}</div>
                    <div className="text-xs text-gray-400">Category</div>
                  </div>
                </>
              ) : (
                <>
                  <div className="w-8 h-8 bg-gradient-to-r from-[#f44336] to-[#ff9800] rounded flex items-center justify-center flex-shrink-0">
                    <FaSearch className="text-white text-sm" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{suggestion.title}</div>
                    <div className="text-xs text-gray-400">Genre</div>
                  </div>
                </>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

SearchAutocomplete.propTypes = {
  className: PropTypes.string,
  placeholder: PropTypes.string,
  onSearch: PropTypes.func
};

export default SearchAutocomplete;
