import { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import GameCard from '../components/GameCard';
import LoadingSpinner from '../components/LoadingSpinner';
import { FaSearch, FaFilter, FaTimes, FaSortAmountDown, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import axios from 'axios'; // Make sure axios is installed in your project
import { getSecureImageUrl } from '../utils/imageUtils'; // Import getSecureImageUrl
// Change process.env to import.meta.env for Vite
import { API_URL } from '../config/env.js';
import { startsWithTerm } from '../utils/searchUtils';

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [games, setGames] = useState([]);
  const [filteredGames, setFilteredGames] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showMobileFilters, setShowMobileFilters] = useState(false);
  
  // Filter states
  const [filters, setFilters] = useState({
    genres: [],
    tags: [],
    priceRange: { min: 0, max: 100 },
    priceTypes: ['free', 'paid', 'credits'],
  });

  // Available filter options
  const [availableGenres, setAvailableGenres] = useState([]);
  const [availableTags, setAvailableTags] = useState([]);

  // Filter visibility states (for collapsible sections)
  const [expandedFilters, setExpandedFilters] = useState({
    genres: true,
    tags: true,
    price: true,
  });

  // Sorting options
  const [sortOption, setSortOption] = useState('relevance');
  const sortOptions = {
    relevance: 'Relevance',
    newest: 'Newest',
    oldest: 'Oldest',
    priceAsc: 'Price (Low to High)',
    priceDesc: 'Price (High to Low)',
    nameAsc: 'Name (A-Z)',
    nameDesc: 'Name (Z-A)'
  };

  // Reference for the search input (for focus)
  const searchInputRef = useRef(null);

  // Fetch games on component mount
  useEffect(() => {
    fetchGames();
    fetchFilterOptions();
    
    // Focus the search input when the page loads
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

// Fetch games from the API
const fetchGames = async () => {
  setLoading(true);
  setError(null);
  
  try {
    const response = await axios.get(`${API_URL}/games`);
    
    // Extract the games array from the response
    // The API returns {games: Array(4), pagination: {...}}
    const gamesArray = response.data.games || [];
    
    if (!Array.isArray(gamesArray)) {
      console.error('Expected games array not found in response:', response.data);
      throw new Error('Invalid response format from API');
    }
    
    // Transform the database data to match the expected format for GameCard
    const formattedGames = gamesArray.map(game => ({
      id: game.id,
      title: game.title,
      description: game.description,
      image: getSecureImageUrl(game.cardImage || game.image), // Use getSecureImageUrl
      hoverGif: getSecureImageUrl(game.animationGif), // Use getSecureImageUrl
      genre: game.genre,
      tags: game.tags ? game.tags.split(',').map(tag => tag.trim()) : [],
      paymentType: game.priceModel,
      price: game.priceModel === 'paid' ? `$${parseFloat(game.price).toFixed(2)}` : 
             game.priceModel === 'credits' ? `${game.creditPrice} Credits` : 'Free',
      releaseDate: new Date(game.releaseDate)
    }));
    
    setGames(formattedGames);
  } catch (err) {
    console.error('Error fetching games:', err);
    setError('Failed to load games. Please try again later.');
  } finally {
    setLoading(false);
  }
};

  // Fetch available filter options from the API
  const fetchFilterOptions = async () => {
    try {
      // Get unique genres
      const genresResponse = await axios.get(`${API_URL}/games/genres`);
      setAvailableGenres(genresResponse.data);
      
      // Get unique tags
      const tagsResponse = await axios.get(`${API_URL}/games/tags`);
      setAvailableTags(tagsResponse.data);
    } catch (err) {
      console.error('Error fetching filter options:', err);
    }
  };

  // Apply filters and search query to games list
  useEffect(() => {
    if (games.length === 0) return;

    let results = [...games];

    // Apply search query with improved word-boundary matching
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();

      results = results.filter(game => {
        // Only include games where title, genre, description, or tags START with the search term
        // No fallback partial matches to ensure strict word-boundary matching
        const titleStartsMatch = startsWithTerm(game.title, query);
        const genreStartsMatch = startsWithTerm(game.genre, query);
        const descStartsMatch = startsWithTerm(game.description, query);

        // Handle tags properly - check each individual tag, not the whole tags string
        let tagsStartsMatch = false;
        if (game.tags) {
          if (Array.isArray(game.tags)) {
            // If tags is an array
            tagsStartsMatch = game.tags.some(tag => startsWithTerm(tag, query));
          } else if (typeof game.tags === 'string') {
            // If tags is a comma-separated string
            const tagArray = game.tags.split(',').map(tag => tag.trim());
            tagsStartsMatch = tagArray.some(tag => startsWithTerm(tag, query));
          }
        }

        return titleStartsMatch || genreStartsMatch || descStartsMatch || tagsStartsMatch;
      });

      // Sort results by relevance (title matches first, then genre, then tags, then description)
      results = results.sort((a, b) => {
        const aStartsTitle = startsWithTerm(a.title, query);
        const bStartsTitle = startsWithTerm(b.title, query);
        const aStartsGenre = startsWithTerm(a.genre, query);
        const bStartsGenre = startsWithTerm(b.genre, query);

        // Handle tags properly for sorting
        const aStartsTags = a.tags ? (
          Array.isArray(a.tags)
            ? a.tags.some(tag => startsWithTerm(tag, query))
            : a.tags.split(',').map(tag => tag.trim()).some(tag => startsWithTerm(tag, query))
        ) : false;

        const bStartsTags = b.tags ? (
          Array.isArray(b.tags)
            ? b.tags.some(tag => startsWithTerm(tag, query))
            : b.tags.split(',').map(tag => tag.trim()).some(tag => startsWithTerm(tag, query))
        ) : false;

        const aStartsDesc = startsWithTerm(a.description, query);
        const bStartsDesc = startsWithTerm(b.description, query);

        // Priority order: Title > Genre > Tags > Description
        if (aStartsTitle && !bStartsTitle) return -1;
        if (!aStartsTitle && bStartsTitle) return 1;
        if (aStartsGenre && !bStartsGenre) return -1;
        if (!aStartsGenre && bStartsGenre) return 1;
        if (aStartsTags && !bStartsTags) return -1;
        if (!aStartsTags && bStartsTags) return 1;
        if (aStartsDesc && !bStartsDesc) return -1;
        if (!aStartsDesc && bStartsDesc) return 1;

        // If both have same priority, sort alphabetically
        return a.title.localeCompare(b.title);
      });
    }

    // Apply genre filter
    if (filters.genres.length > 0) {
      results = results.filter(game => 
        filters.genres.some(genre => 
          game.genre.toLowerCase() === genre.toLowerCase()
        )
      );
    }

    // Apply tag filter
    if (filters.tags.length > 0) {
      results = results.filter(game => 
        game.tags && game.tags.some(tag => 
          filters.tags.some(selectedTag => 
            tag.toLowerCase() === selectedTag.toLowerCase()
          )
        )
      );
    }

    // Apply price type filter
    if (filters.priceTypes.length > 0 && !filters.priceTypes.includes('all')) {
      results = results.filter(game => 
        filters.priceTypes.includes(game.paymentType)
      );
    }

    // Apply price range filter
    results = results.filter(game => {
      if (game.paymentType === 'free') return true;
      if (game.paymentType === 'paid') {
        const price = parseFloat(game.price.replace(/[^0-9.]/g, ''));
        return price >= filters.priceRange.min && price <= filters.priceRange.max;
      }
      return true;
    });

    // Apply sorting
    results = sortGames(results, sortOption);

    setFilteredGames(results);
    
    // Update URL search params
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    if (filters.genres.length) params.set('genres', filters.genres.join(','));
    if (filters.tags.length) params.set('tags', filters.tags.join(','));
    if (sortOption !== 'relevance') params.set('sort', sortOption);
    setSearchParams(params, { replace: true });
    
  }, [games, searchQuery, filters, sortOption, setSearchParams]);

  // Sort games based on selected option
  const sortGames = (gamesArray, option) => {
    switch (option) {
      case 'newest':
        return [...gamesArray].sort((a, b) => new Date(b.releaseDate) - new Date(a.releaseDate));
      case 'oldest':
        return [...gamesArray].sort((a, b) => new Date(a.releaseDate) - new Date(b.releaseDate));
      case 'priceAsc':
        return [...gamesArray].sort((a, b) => {
          const priceA = a.paymentType === 'free' ? 0 : parseFloat(a.price?.replace(/[^0-9.]/g, '') || 0);
          const priceB = b.paymentType === 'free' ? 0 : parseFloat(b.price?.replace(/[^0-9.]/g, '') || 0);
          return priceA - priceB;
        });
      case 'priceDesc':
        return [...gamesArray].sort((a, b) => {
          const priceA = a.paymentType === 'free' ? 0 : parseFloat(a.price?.replace(/[^0-9.]/g, '') || 0);
          const priceB = b.paymentType === 'free' ? 0 : parseFloat(b.price?.replace(/[^0-9.]/g, '') || 0);
          return priceB - priceA;
        });
      case 'nameAsc':
        return [...gamesArray].sort((a, b) => a.title.localeCompare(b.title));
      case 'nameDesc':
        return [...gamesArray].sort((a, b) => b.title.localeCompare(a.title));
      default: // relevance
        return gamesArray;
    }
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle filter changes
  const toggleGenreFilter = (genre) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      genres: prevFilters.genres.includes(genre)
        ? prevFilters.genres.filter(g => g !== genre)
        : [...prevFilters.genres, genre]
    }));
  };

  const toggleTagFilter = (tag) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      tags: prevFilters.tags.includes(tag)
        ? prevFilters.tags.filter(t => t !== tag)
        : [...prevFilters.tags, tag]
    }));
  };

  const togglePriceTypeFilter = (type) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      priceTypes: prevFilters.priceTypes.includes(type)
        ? prevFilters.priceTypes.filter(t => t !== type)
        : [...prevFilters.priceTypes, type]
    }));
  };

  const handlePriceRangeChange = (key, value) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      priceRange: {
        ...prevFilters.priceRange,
        [key]: value
      }
    }));
  };

  // Toggle filter section visibility
  const toggleFilterSection = (section) => {
    setExpandedFilters(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Clear all filters and search
  const clearAllFilters = () => {
    setSearchQuery('');
    setFilters({
      genres: [],
      tags: [],
      priceRange: { min: 0, max: 100 },
      priceTypes: ['free', 'paid', 'credits'],
    });
    setSortOption('relevance');
    setSearchParams({});
  };

  // Count total active filters for the badge
  const countActiveFilters = () => {
    let count = 0;
    count += filters.genres.length;
    count += filters.tags.length;
    if (filters.priceTypes.length < 3) count += 1; // If not all price types are selected
    return count;
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="bg-gray-800 border-b border-gray-700 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-lg" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search for games..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-full pl-12 pr-12 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20 transition-all duration-200"
              />
              {searchQuery && (
                <button 
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-400 transition-colors duration-200" 
                  onClick={() => setSearchQuery('')}
                  aria-label="Clear search"
                >
                  <FaTimes />
                </button>
              )}
            </div>
            
            <button 
              className="lg:hidden flex items-center gap-2 px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors duration-200 relative"
              onClick={() => setShowMobileFilters(!showMobileFilters)}
            >
              <FaFilter />
              <span>Filters</span>
              {countActiveFilters() > 0 && (
                <span className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                  {countActiveFilters()}
                </span>
              )}
            </button>
          </div>
          
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <FaSortAmountDown className="text-gray-400" />
              <select 
                value={sortOption} 
                onChange={(e) => setSortOption(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500 transition-colors duration-200"
              >
                {Object.entries(sortOptions).map(([value, label]) => (
                  <option key={value} value={value}>{label}</option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center gap-4">
              {(countActiveFilters() > 0 || searchQuery) && (
                <button 
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg font-medium transition-colors duration-200"
                  onClick={clearAllFilters}
                >
                  Clear All
                </button>
              )}
              
              <div className="text-gray-300 font-medium">
                {loading ? 'Searching...' : `${filteredGames.length} games found`}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6 flex gap-6">
        <div className={`w-80 bg-gray-800 rounded-lg p-6 border border-gray-700 h-fit sticky top-6 ${
          showMobileFilters 
            ? 'fixed inset-0 z-50 w-full h-full overflow-y-auto lg:relative lg:inset-auto lg:w-80 lg:h-fit lg:z-auto' 
            : 'hidden lg:block'
        }`}>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">Filters</h2>
            <button 
              className="lg:hidden text-gray-400 hover:text-red-400 transition-colors duration-200" 
              onClick={() => setShowMobileFilters(false)}
              aria-label="Close filters"
            >
              <FaTimes />
            </button>
          </div>
          
          {/* Genres Filter */}
          <div className="mb-6">
            <div 
              className="flex items-center justify-between cursor-pointer py-2 border-b border-gray-700 mb-4" 
              onClick={() => toggleFilterSection('genres')}
            >
              <h3 className="text-lg font-semibold text-white">Genres</h3>
              <span className="text-gray-400">
                {expandedFilters.genres ? <FaChevronUp /> : <FaChevronDown />}
              </span>
            </div>
            
            {expandedFilters.genres && (
              <div className="grid grid-cols-2 gap-2">
                {availableGenres.map(genre => (
                  <label key={genre} className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.genres.includes(genre)}
                      onChange={() => toggleGenreFilter(genre)}
                      className="hidden"
                    />
                    <span className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 w-full text-center ${
                      filters.genres.includes(genre) 
                        ? 'bg-red-500 text-white' 
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}>
                      {genre}
                    </span>
                  </label>
                ))}
              </div>
            )}
          </div>
          
          {/* Tags Filter */}
          <div className="mb-6">
            <div 
              className="flex items-center justify-between cursor-pointer py-2 border-b border-gray-700 mb-4" 
              onClick={() => toggleFilterSection('tags')}
            >
              <h3 className="text-lg font-semibold text-white">Tags</h3>
              <span className="text-gray-400">
                {expandedFilters.tags ? <FaChevronUp /> : <FaChevronDown />}
              </span>
            </div>
            
            {expandedFilters.tags && (
              <div className="flex flex-wrap gap-2">
                {availableTags.map(tag => (
                  <label key={tag} className="cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.tags.includes(tag)}
                      onChange={() => toggleTagFilter(tag)}
                      className="hidden"
                    />
                    <span className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                      filters.tags.includes(tag) 
                        ? 'bg-red-500 text-white' 
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}>
                      {tag}
                    </span>
                  </label>
                ))}
              </div>
            )}
          </div>
          
          {/* Price Filter */}
          <div className="mb-6">
            <div 
              className="flex items-center justify-between cursor-pointer py-2 border-b border-gray-700 mb-4" 
              onClick={() => toggleFilterSection('price')}
            >
              <h3 className="text-lg font-semibold text-white">Price</h3>
              <span className="text-gray-400">
                {expandedFilters.price ? <FaChevronUp /> : <FaChevronDown />}
              </span>
            </div>
            
            {expandedFilters.price && (
              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-2">
                  {[['free', 'Free'], ['paid', 'Paid'], ['credits', 'Credits']].map(([type, label]) => (
                    <label key={type} className="cursor-pointer">
                      <input
                        type="checkbox"
                        checked={filters.priceTypes.includes(type)}
                        onChange={() => togglePriceTypeFilter(type)}
                        className="hidden"
                      />
                      <span className={`block px-3 py-2 rounded-lg text-sm font-medium text-center transition-all duration-200 ${
                        filters.priceTypes.includes(type) 
                          ? 'bg-red-500 text-white' 
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}>
                        {label}
                      </span>
                    </label>
                  ))}
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-gray-300 mb-3">Price Range ($)</h4>
                  <div className="flex items-center gap-2 mb-3">
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={filters.priceRange.min}
                      onChange={(e) => handlePriceRangeChange('min', parseInt(e.target.value))}
                      className="w-20 px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:outline-none focus:border-red-500"
                    />
                    <span className="text-gray-400">-</span>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={filters.priceRange.max}
                      onChange={(e) => handlePriceRangeChange('max', parseInt(e.target.value))}
                      className="w-20 px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:outline-none focus:border-red-500"
                    />
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={filters.priceRange.max}
                    onChange={(e) => handlePriceRangeChange('max', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                  />
                </div>
              </div>
            )}
          </div>
          
          <button 
            className="lg:hidden w-full mt-6 px-4 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors duration-200" 
            onClick={() => setShowMobileFilters(false)}
          >
            Apply Filters
          </button>
        </div>
        
        <div className="flex-1">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-16">
              <LoadingSpinner size="lg" color="primary" />
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
                <FaTimes className="text-2xl text-red-400" />
              </div>
              <p className="text-red-400 text-lg mb-4">{error}</p>
              <button 
                className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors duration-200"
                onClick={fetchGames}
              >
                Retry
              </button>
            </div>
          ) : filteredGames.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mb-4">
                <FaSearch className="text-2xl text-gray-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">No games found</h3>
              <p className="text-gray-400 mb-6">Try adjusting your search or filters</p>
              <button 
                className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors duration-200"
                onClick={clearAllFilters}
              >
                Clear All Filters
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredGames.map(game => (
                <div key={game.id} className="h-full">
                  <GameCard game={game} />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      
      <style dangerouslySetInnerHTML={{
        __html: `
          .slider::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #ef4444;
            cursor: pointer;
            border: 2px solid #1f2937;
          }
          
          .slider::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #ef4444;
            cursor: pointer;
            border: 2px solid #1f2937;
          }
        `
      }} />
    </div>
  );
};

export default SearchPage;
