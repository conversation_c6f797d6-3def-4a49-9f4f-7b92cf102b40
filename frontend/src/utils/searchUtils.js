/**
 * Search utility functions for consistent search behavior across the application
 */

/**
 * Check if text starts with search term (word boundary matching)
 * @param {string} text - Text to search in
 * @param {string} searchTerm - Term to search for
 * @returns {boolean} - True if text starts with search term or any word starts with search term
 */
export const startsWithTerm = (text, searchTerm) => {
  if (!text || !searchTerm) return false;
  
  const lowerText = text.toLowerCase();
  const lowerSearchTerm = searchTerm.toLowerCase();

  // Check if it starts with the search term
  if (lowerText.startsWith(lowerSearchTerm)) return true;

  // Check if any word starts with the search term
  const words = lowerText.split(/\s+/);
  return words.some(word => word.startsWith(lowerSearchTerm));
};

/**
 * Filter games based on strict word-boundary search
 * @param {Array} games - Array of games to filter
 * @param {string} searchTerm - Search term
 * @returns {Array} - Filtered and sorted games
 */
export const filterGamesBySearch = (games, searchTerm) => {
  if (!searchTerm.trim() || !games.length) return games;

  const filtered = games.filter(game => {
    // Only include games where title, genre, tags, or description STARTS with the search term
    const titleStartsMatch = startsWithTerm(game.title, searchTerm);
    const genreStartsMatch = startsWithTerm(game.genre, searchTerm);
    const tagsStartsMatch = game.tags && startsWithTerm(game.tags, searchTerm);
    const descStartsMatch = startsWithTerm(game.description, searchTerm);

    return titleStartsMatch || genreStartsMatch || tagsStartsMatch || descStartsMatch;
  });

  // Sort results by relevance (title matches first, then genre, then tags, then description)
  return filtered.sort((a, b) => {
    const aStartsTitle = startsWithTerm(a.title, searchTerm);
    const bStartsTitle = startsWithTerm(b.title, searchTerm);
    const aStartsGenre = startsWithTerm(a.genre, searchTerm);
    const bStartsGenre = startsWithTerm(b.genre, searchTerm);
    const aStartsTags = a.tags && startsWithTerm(a.tags, searchTerm);
    const bStartsTags = b.tags && startsWithTerm(b.tags, searchTerm);

    // Priority order: Title > Genre > Tags > Description
    if (aStartsTitle && !bStartsTitle) return -1;
    if (!aStartsTitle && bStartsTitle) return 1;
    if (aStartsGenre && !bStartsGenre) return -1;
    if (!aStartsGenre && bStartsGenre) return 1;
    if (aStartsTags && !bStartsTags) return -1;
    if (!aStartsTags && bStartsTags) return 1;

    // If both have same priority, sort alphabetically
    return a.title.localeCompare(b.title);
  });
};

/**
 * Test the search functionality
 */
export const testSearchFunctionality = () => {
  const testGames = [
    { id: 1, title: "Super Mario", genre: "Platformer", tags: "action,adventure" },
    { id: 2, title: "Racing Game", genre: "Sports", tags: "racing,cars" },
    { id: 3, title: "Action Hero", genre: "Action", tags: "shooter,combat" },
    { id: 4, title: "Space Adventure", genre: "Adventure", tags: "space,exploration" },
    { id: 5, title: "My Super Game", genre: "RPG", tags: "fantasy,magic" },
    { id: 6, title: "Epic Game", genre: "Adventure", tags: "epic,story" }
  ];

  const testCategories = [
    { value: "action", label: "Action" },
    { value: "adventure", label: "Adventure" },
    { value: "sports", label: "Sports" },
    { value: "rpg", label: "RPG" }
  ];

  console.log("Testing search functionality:");

  // Test 1: Search for "S" should only return games starting with "S"
  const sResults = filterGamesBySearch(testGames, "S");
  console.log("Search 'S':", sResults.map(g => g.title));
  // Expected: ["Super Mario", "Space Adventure"] (not "Racing Game" or others)

  // Test 2: Search for "R" should only return games starting with "R"
  const rResults = filterGamesBySearch(testGames, "R");
  console.log("Search 'R':", rResults.map(g => g.title));
  // Expected: ["Racing Game"] (not "Super Mario" or others)

  // Test 3: Search for "e" should NOT return "Adventure" games
  const eResults = filterGamesBySearch(testGames, "e");
  console.log("Search 'e':", eResults.map(g => g.title));
  // Expected: ["Epic Game"] (only games starting with "e", not containing "e")

  // Test 4: Search for "Action" should return games with "Action" in title or genre
  const actionResults = filterGamesBySearch(testGames, "Action");
  console.log("Search 'Action':", actionResults.map(g => g.title));
  // Expected: ["Action Hero"] (title starts with Action)

  // Test 5: Search for "Super" should return games with "Super" at start of title or any word
  const superResults = filterGamesBySearch(testGames, "Super");
  console.log("Search 'Super':", superResults.map(g => g.title));
  // Expected: ["Super Mario", "My Super Game"] (both have "Super" at word boundary)

  // Test 6: Category search - "A" should only match categories starting with "A"
  const aCategoryResults = testCategories.filter(cat => startsWithTerm(cat.label, "A"));
  console.log("Category search 'A':", aCategoryResults.map(c => c.label));
  // Expected: ["Action", "Adventure"] (not "Sports" which contains no "A" at start)

  // Test 7: Category search - "e" should NOT match "Adventure"
  const eCategoryResults = testCategories.filter(cat => startsWithTerm(cat.label, "e"));
  console.log("Category search 'e':", eCategoryResults.map(c => c.label));
  // Expected: [] (no categories start with "e")
};

// Run test in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Uncomment to run tests
  // console.log("=== Search Functionality Test ===");
  // testSearchFunctionality();
  // console.log("=== End Test ===");
}
